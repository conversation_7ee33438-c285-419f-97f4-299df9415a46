"""
Production Chess OCR Prompts
Structured prompt system for chess scoresheet extraction
"""

# Initial OCR extraction prompt
INITIAL_EXTRACTION_PROMPT= """
{
  "task": "chess_scoresheet_OCR",
  "thinking_steps": [
    "Identify all clearly visible text from the scoresheet image.",
    "Check for any ambiguous characters that may be OCR errors.",
    "Detect standard chess notation patterns.",
    "Eliminate impossible character combinations in chess notation.",
    "Apply chess-aware OCR corrections only for ambiguous characters."
  ],
  "role": "Specialized OCR system for chess scoresheets with expert level chess intelligence",
  "rules": {
    "valid_characters": {
      "moves": ["K", "Q", "R", "B", "N", "O", "a-h", "1-8", "x", "+", "#", "=", "-"],
      "annotations": ["!", "?", "(", ")", "{", "}"],
      "results": ["1-0", "0-1", "1/2-1/2", "*"],
      "result_components": ["0", "1", "/", "2", "-", "*"]
    },
    "positional_ocr_fixes": [
      { "from": "t", "to": "f", "context": "file_letter", "example": "Kt3 → Kf3, Rt1 → Rf1" },
      { "from": "0", "to": "O", "context": "castling", "example": "0-0 → O-O, 0-0-0 → O-O-O" },
      { "from": "l", "to": "1", "context": "numbers", "example": "Kfl → Kf1" },
      { "from": "I", "to": "1", "context": "move_numbers", "example": "I2. → 12." },
      { "from": "t", "to": "+", "context": "end_symbol", "condition": "only if at move end", "example": "Kf3t → Kf3+" }
    ],
    "position_based_corrections": {
      "first_position": {
        "rules": "Only piece letters (K,Q,R,B,N) or file letters (a-h) or castling (O) allowed",
        "corrections": [
          { "from": "S", "to": "5", "condition": "never valid as first character" },
          { "from": "6", "to": "G", "condition": "if followed by valid move pattern" },
          { "from": "8", "to": "B", "condition": "if followed by valid move pattern" }
        ]
      },
      "middle_positions": {
        "rules": "File letters (a-h), numbers (1-8), 'x' for capture, '=' for promotion",
        "corrections": [
          { "from": "S", "to": "5", "condition": "numbers expected in rank positions" },
          { "from": "G", "to": "6", "condition": "numbers expected in rank positions" },
          { "from": "B", "to": "8", "condition": "numbers expected in rank positions" },
          { "from": "y", "to": "4", "condition": "numbers expected in rank positions" }
        ]
      },
      "end_positions": {
        "rules": "Rank numbers (1-8), check (+), checkmate (#), annotations (!, ?)",
        "corrections": [
          { "from": "S", "to": "5", "condition": "rank position at move end" },
          { "from": "G", "to": "6", "condition": "rank position at move end" },
          { "from": "B", "to": "8", "condition": "rank position at move end" }
        ]
      },
      "logic": "Analyze character position within move string to determine valid corrections"
    },
    "syntax_validation": {
      "move_end_square": "must be a-h + 1-8 (except castling and annotations)",
      "captures": "maximum one 'x' per move, preceded by piece letter or file letter",
      "check_mate_position": "symbols '+' or '#' only at the end of a move",
      "castling": {
        "valid": ["O-O", "O-O-O"],
        "invalid": ["0-0", "0-0-0", "OOO", "000"]
      },
      "first_character_rules": {
        "valid": ["K", "Q", "R", "B", "N", "a-h", "O"],
        "invalid_unless_clear": ["H", "T", "0"]
      },
      "pawn_moves": "file letter (a-h) + destination square (e.g., e4, axb5)",
      "piece_moves": "piece letter (K/Q/R/B/N) + optional disambiguation + destination square",
      "file_letter_case": "lowercase only in moves (e.g., e4, Kf3, not E4, KF3)"
    },
    "valid_move_endings": {
      "square": "a-h + 1-8",
      "check": "+",
      "checkmate": "#",
      "castling": ["O-O", "O-O-O"],
      "promotion": ["=Q", "=R", "=B", "=N", "=K"],
      "annotations": ["!", "?", "!!", "??", "!?", "?!"]
    },
    "case_sensitivity_rules": {
      "uppercase_required": ["K", "Q", "R", "B", "N", "O"],
      "lowercase_required": ["a-h"],
      "case_irrelevant": ["1-8", "x", "+", "#", "=", "-", "!", "?"]
    },
    "move_pattern_analysis": {
      "standard_patterns": [
        "Piece + destination (e.g., Kf3, Qd5)",
        "Piece + disambiguation + destination (e.g., Rad1, N1f3)",
        "Pawn move (e.g., e4, d5)",
        "Pawn capture (e.g., exd5, axb6)",
        "Castling (O-O, O-O-O)"
      ],
      "position_validation": {
        "character_1": "K,Q,R,B,N,a-h,O only",
        "character_2": "a-h,1-8,x,- only (disambiguation, destination, capture)",
        "character_3": "1-8,O only (destination rank, castling continuation)",
        "character_4_plus": "check/mate symbols, annotations, castling continuation"
      },
      "correction_logic": "If character doesn't fit position rules, check for common OCR mistakes based on position context"
    },
    "strict_fix_policy": "Only fix clearly ambiguous characters based on chess notation context. If handwriting is clear but creates invalid notation, preserve as written and flag for review."
  },
  "move_number_handling": {
    "preserve_format": true,
    "no_recalculation": true,
    "common_formats": ["1."],
    "handle_missing": "interpolate only if pattern is clear"
  },
  "quality_control": {
    "flag_suspicious": [
      "moves that don't follow algebraic notation",
      "impossible piece movements", 
      "ambiguous characters that couldn't be resolved",
      "missing move numbers in sequence"
    ]
  },
  "output_format": {
    "primary": "Standard PGN format with metadata tags and moves - NO ADDITIONAL COMMENTS OR EXPLANATIONS",
    "downstream_requirement": "Your result will be sent to a chess engine to validate so response format must be engine-ready PGN format",
    "include": {
      "player_names": "if clearly visible",
      "date": "if present on scoresheet", 
      "event": "if specified",
      "result": "standard format (1-0, 0-1, 1/2-1/2, *)",
      "moves": "algebraic notation with move numbers"
    },
    "metadata_preservation": "maintain original scoresheet information in PGN tags"
  },
  "processing_priorities": [
    "1. Preserve clear, readable text exactly as written",
    "2. Apply OCR corrections only for genuinely ambiguous characters", 
    "3. Validate against chess notation rules",
    "4. Flag impossible or highly unusual moves for review",
    "5. Maintain original formatting structure where possible"
  ],
  "critical_instruction": "RESPOND WITH CLEAN PGN FORMAT ONLY. NO EXPLANATIONS, NO COMMENTS, NO ANALYSIS. JUST THE PGN DATA THAT CAN BE DIRECTLY FED TO A CHESS ENGINE."
}
"""

# Error correction prompt for validation feedback
ERROR_CORRECTION_PROMPT = """<thinking>
The chess validator found an illegal move. Let me re-examine the specific area in the image:
1. What does the handwriting actually show for this move?
2. Could this be an OCR error (t/f, 0/O, l/1, etc.)?
3. Is the handwriting clear or ambiguous?
4. What's the minimal change that would make this legal?

I should only correct obvious OCR errors, not predict moves.

Structure of a .pgn File
A .pgn file typically has two main sections:

   - Tag Pair Section: Metadata about the game, enclosed in square brackets.
   - Movetext Section: The sequence of moves made during the game.
</thinking>

CHESS MOVE CORRECTION - OCR ERROR DETECTED

Previous extraction: "{illegal_move}" at move {move_number}
Chess validator reports: ILLEGAL MOVE

TASK: Re-examine the handwritten text for move {move_number} in the image.

CORRECTION RULES:
1. Look ONLY at the specific move area in the image
2. Apply OCR corrections ONLY if handwriting is ambiguous:
   - 't' → 'f' (common OCR error)
   - '0' → 'O' (zero vs letter O)
   - 'l' → '1' (lowercase L vs number)
   - '5' → 'S' (if unclear handwriting)

3. SYNTAX CHECKS:
   - Move must end with valid square (a-h + 1-8)
   - '+' or '#' only at end, not middle of move
   - Single 'x' for captures, not multiple
   - First character must be valid piece/pawn/castling letter

3. FORBIDDEN:
   - Do NOT predict what move should be played
   - Do NOT change clearly written text
   - Do NOT use chess strategy

Downstream:
Your OCR will be directly send to the stockfish engine to validate so make sure you adhere to rules strictly.

RESPONSE:
- If you see an OCR error: Output corrected move only (e.g., "Kf3")
- If handwriting clearly shows "{illegal_move}": Output "KEEP_ORIGINAL"

What do you actually see written for move {move_number}?"""

# Validation prompt for final check
VALIDATION_PROMPT = """<thinking>
I need to verify this PGN is properly formatted:
1. Are all tags in [brackets]?
2. Are moves in correct numbered format?
3. Is the structure valid?
4. Are there any formatting issues?
</thinking>

FINAL PGN VALIDATION

Review this PGN and ensure proper formatting:

{pgn_content}

Check:
1. Tags in [Tag "Value"] format
2. Moves in "1. e4 e5 2. Nf3 Nc6" format
3. No extra text or explanations
4. Proper line breaks

Output the corrected PGN if needed, or "VALID" if no changes required."""

def get_initial_prompt():
    """Get the initial OCR extraction prompt"""
    return INITIAL_EXTRACTION_PROMPT

def get_correction_prompt(illegal_move, move_number):
    """Get the error correction prompt"""
    return ERROR_CORRECTION_PROMPT.format(
        illegal_move=illegal_move,
        move_number=move_number
    )

def get_validation_prompt(pgn_content):
    """Get the final validation prompt"""
    return VALIDATION_PROMPT.format(pgn_content=pgn_content)