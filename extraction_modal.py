"""
Extraction Validation Modal Component
Provides modal-like functionality for displaying text extraction validation
"""

import streamlit as st
import base64
from PIL import Image
import io
from text_detection_validator import TextDetectionValidator
import logging

logger = logging.getLogger(__name__)

class ExtractionModal:
    def __init__(self):
        """Initialize the extraction modal component"""
        self.validator = TextDetectionValidator()
    
    def show_extraction_validation(self, image_bytes: bytes, extracted_text: str = None):
        """
        Display the extraction validation modal
        
        Args:
            image_bytes: Original image data
            extracted_text: Extracted PGN text (optional)
        """
        # Create a unique key for this modal session
        modal_key = "extraction_modal"
        
        # Check if modal should be shown
        if st.session_state.get(f"{modal_key}_show", False):
            self._render_modal_content(image_bytes, extracted_text, modal_key)
    
    def _render_modal_content(self, image_bytes: bytes, extracted_text: str, modal_key: str):
        """
        Render the modal content with validation overlay

        Args:
            image_bytes: Original image data
            extracted_text: Extracted PGN text
            modal_key: Unique key for this modal
        """
        # Use Streamlit's dialog functionality (available in newer versions)
        # For compatibility, we'll use a full-width container with prominent styling
        st.markdown("""
        <style>
        .validation-modal {
            background-color: #f0f2f6;
            border: 2px solid #1f77b4;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .modal-header {
            background-color: #1f77b4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        </style>
        """, unsafe_allow_html=True)

        # Create modal container with custom styling
        with st.container():
            st.markdown('<div class="validation-modal">', unsafe_allow_html=True)

            # Modal header
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown('<div class="modal-header"><h3>🔍 Text Extraction Validation</h3></div>',
                           unsafe_allow_html=True)
            with col2:
                if st.button("✕ Close", key=f"{modal_key}_close", type="secondary"):
                    st.session_state[f"{modal_key}_show"] = False
                    st.rerun()
            
            st.markdown("---")
            
            # Show loading spinner while processing
            with st.spinner("🔍 Creating validation overlay..."):
                # Create validation overlay with extracted text
                overlay_image_bytes = self.validator.create_validation_overlay(
                    image_bytes, extracted_text or ""
                )
            
            # Display results in tabs
            tab1, tab2, tab3 = st.tabs(["📊 Validation Overlay", "📝 Extracted Content", "📈 Statistics"])

            with tab1:
                st.markdown("**Original image with extracted text overlay:**")
                st.image(overlay_image_bytes, use_container_width=True,
                        caption="Validation overlay showing extracted PGN content")
                
                # Download button for overlay image
                overlay_b64 = base64.b64encode(overlay_image_bytes).decode()
                st.markdown(f"""
                <a href="data:image/jpeg;base64,{overlay_b64}" download="validation_overlay.jpg">
                    <button style="background-color: #4CAF50; color: white; padding: 10px 20px; 
                           border: none; border-radius: 4px; cursor: pointer;">
                        📥 Download Overlay Image
                    </button>
                </a>
                """, unsafe_allow_html=True)
            
            with tab2:
                st.markdown("**Extracted PGN Content:**")

                if extracted_text:
                    # Show the full extracted text in a code block
                    st.code(extracted_text, language="text")

                    # Show a preview of moves
                    moves = self.validator._extract_moves_from_pgn(extracted_text)
                    if moves:
                        st.markdown("**First 10 moves:**")
                        cols = st.columns(5)
                        for i, move in enumerate(moves[:10]):
                            with cols[i % 5]:
                                st.write(f"{i+1}. {move}")
                else:
                    st.warning("No extracted text available.")
            
            with tab3:
                if extracted_text:
                    # Generate statistics report
                    with st.spinner("📊 Calculating statistics..."):
                        stats_report = self.validator.validate_extraction_accuracy(
                            image_bytes, extracted_text
                        )

                    # Display statistics
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Total Moves", stats_report['total_moves'])

                    with col2:
                        st.metric("Content Lines", stats_report['content_lines'])

                    with col3:
                        st.metric("Metadata Lines", stats_report['metadata_lines'])

                    # Show validation message
                    st.info(stats_report['validation_message'])

                    # Show first few moves
                    if stats_report['moves_list']:
                        st.markdown("**First few moves:**")
                        st.write(", ".join(stats_report['moves_list']))
                else:
                    st.info("No extracted text provided for statistics.")
            
            # Instructions
            st.markdown("---")
            st.markdown("""
            **How to use this validation:**
            1. **Validation Overlay**: Check if red boxes accurately surround all text in your scoresheet
            2. **Detected Text**: Review all detected text elements and their confidence scores
            3. **Accuracy Report**: Compare detected text with your extracted PGN to identify discrepancies
            
            **Tips for better accuracy:**
            - Ensure good lighting and minimal glare in your photos
            - Keep the scoresheet as flat as possible
            - Use the image preprocessing option for better results
            """)

            st.markdown('</div>', unsafe_allow_html=True)

def show_extraction_modal_button():
    """
    Show the button to trigger extraction validation modal
    
    Returns:
        True if button was clicked, False otherwise
    """
    return st.button("🔍 Check Extraction", 
                    help="Validate text extraction by showing detected text boxes on the original image",
                    use_container_width=True)

def trigger_extraction_modal():
    """Trigger the extraction modal to show"""
    st.session_state["extraction_modal_show"] = True
