"""
Extraction Validation Modal Component
Provides modal-like functionality for displaying text extraction validation
"""

import streamlit as st
import base64
from PIL import Image
import io
from text_detection_validator import TextDetectionValidator
import logging

logger = logging.getLogger(__name__)

class ExtractionModal:
    def __init__(self):
        """Initialize the extraction modal component"""
        self.validator = TextDetectionValidator()
    
    def show_extraction_validation(self, image_bytes: bytes, extracted_text: str = None):
        """
        Display the extraction validation modal
        
        Args:
            image_bytes: Original image data
            extracted_text: Extracted PGN text (optional)
        """
        # Create a unique key for this modal session
        modal_key = "extraction_modal"
        
        # Check if modal should be shown
        if st.session_state.get(f"{modal_key}_show", False):
            self._render_modal_content(image_bytes, extracted_text, modal_key)
    
    def _render_modal_content(self, image_bytes: bytes, extracted_text: str, modal_key: str):
        """
        Render the modal content with validation overlay

        Args:
            image_bytes: Original image data
            extracted_text: Extracted PGN text
            modal_key: Unique key for this modal
        """
        # Use Streamlit's dialog functionality (available in newer versions)
        # For compatibility, we'll use a full-width container with prominent styling
        st.markdown("""
        <style>
        .validation-modal {
            background-color: #f0f2f6;
            border: 2px solid #1f77b4;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
        }
        .modal-header {
            background-color: #1f77b4;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        </style>
        """, unsafe_allow_html=True)

        # Create modal container with custom styling
        with st.container():
            st.markdown('<div class="validation-modal">', unsafe_allow_html=True)

            # Modal header
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown('<div class="modal-header"><h3>🔍 Text Extraction Validation</h3></div>',
                           unsafe_allow_html=True)
            with col2:
                if st.button("✕ Close", key=f"{modal_key}_close", type="secondary"):
                    st.session_state[f"{modal_key}_show"] = False
                    st.rerun()
            
            st.markdown("---")
            
            # Show loading spinner while processing
            with st.spinner("🔍 Analyzing text detection..."):
                # Detect text with bounding boxes
                detected_texts = self.validator.detect_text_with_boxes(image_bytes)
                
                # Create validation overlay
                overlay_image_bytes = self.validator.create_validation_overlay(
                    image_bytes, detected_texts
                )
            
            # Display results in tabs
            tab1, tab2, tab3 = st.tabs(["📊 Validation Overlay", "📝 Detected Text", "📈 Accuracy Report"])
            
            with tab1:
                st.markdown("**Original image with detected text bounding boxes:**")
                st.image(overlay_image_bytes, use_container_width=True, 
                        caption="Red boxes show detected text areas with confidence scores")
                
                # Download button for overlay image
                overlay_b64 = base64.b64encode(overlay_image_bytes).decode()
                st.markdown(f"""
                <a href="data:image/jpeg;base64,{overlay_b64}" download="validation_overlay.jpg">
                    <button style="background-color: #4CAF50; color: white; padding: 10px 20px; 
                           border: none; border-radius: 4px; cursor: pointer;">
                        📥 Download Overlay Image
                    </button>
                </a>
                """, unsafe_allow_html=True)
            
            with tab2:
                st.markdown("**All detected text elements:**")
                
                if detected_texts:
                    # Create a table of detected texts
                    for i, detection in enumerate(detected_texts, 1):
                        with st.expander(f"Detection {i}: '{detection['text']}' (Confidence: {detection['confidence']:.2f})"):
                            col_text, col_coords = st.columns(2)
                            
                            with col_text:
                                st.write(f"**Text:** {detection['text']}")
                                st.write(f"**Confidence:** {detection['confidence']:.3f}")
                            
                            with col_coords:
                                bbox = detection['bbox']
                                st.write("**Bounding Box Coordinates:**")
                                for j, (x, y) in enumerate(bbox):
                                    st.write(f"Point {j+1}: ({x:.0f}, {y:.0f})")
                else:
                    st.warning("No text detected in the image.")
            
            with tab3:
                if extracted_text:
                    # Generate accuracy report
                    with st.spinner("📊 Calculating accuracy metrics..."):
                        accuracy_report = self.validator.validate_extraction_accuracy(
                            image_bytes, extracted_text
                        )
                    
                    # Display accuracy metrics
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("Accuracy", f"{accuracy_report['accuracy_percentage']:.1f}%")
                    
                    with col2:
                        st.metric("Detected Words", accuracy_report['detected_words_count'])
                    
                    with col3:
                        st.metric("PGN Words", accuracy_report['pgn_words_count'])
                    
                    # Show detailed comparison
                    if accuracy_report['missing_in_pgn']:
                        st.warning("**Words detected in image but missing in PGN:**")
                        st.write(", ".join(accuracy_report['missing_in_pgn']))
                    
                    if accuracy_report['extra_in_pgn']:
                        st.info("**Words in PGN but not detected in image:**")
                        st.write(", ".join(accuracy_report['extra_in_pgn']))
                    
                    if accuracy_report['accuracy_percentage'] >= 90:
                        st.success("✅ Excellent extraction accuracy!")
                    elif accuracy_report['accuracy_percentage'] >= 75:
                        st.warning("⚠️ Good extraction accuracy, minor issues detected.")
                    else:
                        st.error("❌ Low extraction accuracy, significant issues detected.")
                else:
                    st.info("No extracted text provided for accuracy comparison.")
            
            # Instructions
            st.markdown("---")
            st.markdown("""
            **How to use this validation:**
            1. **Validation Overlay**: Check if red boxes accurately surround all text in your scoresheet
            2. **Detected Text**: Review all detected text elements and their confidence scores
            3. **Accuracy Report**: Compare detected text with your extracted PGN to identify discrepancies
            
            **Tips for better accuracy:**
            - Ensure good lighting and minimal glare in your photos
            - Keep the scoresheet as flat as possible
            - Use the image preprocessing option for better results
            """)

            st.markdown('</div>', unsafe_allow_html=True)

def show_extraction_modal_button():
    """
    Show the button to trigger extraction validation modal
    
    Returns:
        True if button was clicked, False otherwise
    """
    return st.button("🔍 Check Extraction", 
                    help="Validate text extraction by showing detected text boxes on the original image",
                    use_container_width=True)

def trigger_extraction_modal():
    """Trigger the extraction modal to show"""
    st.session_state["extraction_modal_show"] = True
