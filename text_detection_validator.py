"""
Text Extraction Validator for Chess Scoresheet
Simple overlay system to show extracted text on the original image for validation
"""

from PIL import Image, ImageDraw, ImageFont
import io
import logging
from typing import List, Dict, Any
import re

logger = logging.getLogger(__name__)

class TextDetectionValidator:
    def __init__(self):
        """Initialize the text detection validator"""
        pass
    
    def create_simple_validation_overlay(self, image_bytes: bytes, extracted_pgn: str) -> bytes:
        """
        Create a simple validation overlay showing the extracted PGN text on the image

        Args:
            image_bytes: Original image data
            extracted_pgn: The PGN text extracted by LLM

        Returns:
            Image bytes with text overlay for validation
        """
        try:
            # Convert to PIL Image
            image = Image.open(io.BytesIO(image_bytes))

            # Create a copy for drawing
            overlay_image = image.copy()
            draw = ImageDraw.Draw(overlay_image)

            # Try to load a font, fallback to default if not available
            try:
                font = ImageFont.truetype("arial.ttf", 14)
                title_font = ImageFont.truetype("arial.ttf", 18)
            except:
                font = ImageFont.load_default()
                title_font = ImageFont.load_default()

            # Create a text overlay showing the extracted content
            overlay_text = f"Extracted PGN Content:\n{extracted_pgn[:500]}..."

            # Position the text overlay at the top-right corner
            img_width, _ = image.size
            text_x = img_width - 400
            text_y = 20

            # Draw background rectangle for text
            text_lines = overlay_text.split('\n')
            max_line_width = max([draw.textbbox((0, 0), line, font=font)[2] for line in text_lines])
            text_height = len(text_lines) * 20

            # Draw semi-transparent background
            overlay_bg = Image.new('RGBA', (max_line_width + 20, text_height + 20), (255, 255, 255, 200))
            overlay_image.paste(overlay_bg, (text_x - 10, text_y - 10), overlay_bg)

            # Draw text
            for i, line in enumerate(text_lines):
                draw.text((text_x, text_y + i * 20), line, fill='black', font=font)

            # Add title
            draw.text((text_x, text_y - 30), "EXTRACTED TEXT VALIDATION", fill='red', font=title_font)

            # Convert back to bytes
            output_buffer = io.BytesIO()
            overlay_image.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()

        except Exception as e:
            logger.error(f"Error creating validation overlay: {e}")
            return image_bytes  # Return original image if overlay fails

    def _extract_moves_from_pgn(self, pgn_text: str) -> List[str]:
        """Extract individual moves from PGN text"""
        # Remove metadata lines (lines starting with [)
        lines = [line for line in pgn_text.split('\n') if not line.strip().startswith('[')]
        moves_text = ' '.join(lines)

        # Extract moves using regex
        move_pattern = r'\d+\.\s*([^\s]+)(?:\s+([^\s]+))?'
        matches = re.findall(move_pattern, moves_text)

        moves = []
        for white_move, black_move in matches:
            if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                moves.append(white_move)
            if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                moves.append(black_move)

        return moves

    def create_validation_overlay(self, image_bytes: bytes, extracted_pgn: str) -> bytes:
        """
        Create a simple validation overlay showing the extracted PGN text

        Args:
            image_bytes: Original image data
            extracted_pgn: The extracted PGN text

        Returns:
            Image bytes with text overlay
        """
        # For now, just use the simple overlay method
        return self.create_simple_validation_overlay(image_bytes, extracted_pgn)
    
    def validate_extraction_accuracy(self, image_bytes: bytes, extracted_pgn: str) -> Dict[str, Any]:
        """
        Simple validation showing basic PGN statistics

        Args:
            image_bytes: Original image data (not used in simple version)
            extracted_pgn: PGN text extracted by LLM

        Returns:
            Dictionary with validation results
        """
        # Extract moves from PGN
        moves = self._extract_moves_from_pgn(extracted_pgn)

        # Count lines and words
        lines = [line.strip() for line in extracted_pgn.split('\n') if line.strip()]
        metadata_lines = [line for line in lines if line.startswith('[')]
        content_lines = [line for line in lines if not line.startswith('[')]

        return {
            'total_moves': len(moves),
            'total_lines': len(lines),
            'metadata_lines': len(metadata_lines),
            'content_lines': len(content_lines),
            'moves_list': moves[:10],  # First 10 moves for preview
            'validation_message': f"Extracted {len(moves)} moves from {len(content_lines)} content lines"
        }
