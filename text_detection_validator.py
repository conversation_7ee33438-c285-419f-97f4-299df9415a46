"""
Text Detection Validator for Chess Scoresheet OCR
Provides bounding box detection and validation overlay functionality
"""

import cv2
import numpy as np
import easyocr
from PIL import Image, ImageDraw, ImageFont
import io
import base64
import logging
from typing import List, Tuple, Dict, Any
import re

logger = logging.getLogger(__name__)

class TextDetectionValidator:
    def __init__(self):
        """Initialize the text detection validator with EasyOCR"""
        try:
            # Initialize EasyOCR reader for English
            self.reader = easyocr.Reader(['en'], gpu=False)
            logger.info("EasyOCR initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.reader = None
    
    def detect_text_with_boxes(self, image_bytes: bytes) -> List[Dict[str, Any]]:
        """
        Detect text in image and return bounding boxes with text
        
        Args:
            image_bytes: Image data as bytes
            
        Returns:
            List of dictionaries containing:
            - text: detected text
            - bbox: bounding box coordinates [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
            - confidence: detection confidence
        """
        if not self.reader:
            logger.error("EasyOCR not initialized")
            return []
        
        try:
            # Convert bytes to numpy array
            nparr = np.frombuffer(image_bytes, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            # Use EasyOCR to detect text with bounding boxes
            results = self.reader.readtext(img)
            
            detected_texts = []
            for (bbox, text, confidence) in results:
                # Filter out low confidence detections
                if confidence > 0.3:  # Adjust threshold as needed
                    # Clean up text - remove extra spaces and normalize
                    cleaned_text = re.sub(r'\s+', ' ', text.strip())
                    
                    # Only include if it looks like chess notation or numbers
                    if self._is_chess_relevant_text(cleaned_text):
                        detected_texts.append({
                            'text': cleaned_text,
                            'bbox': bbox,
                            'confidence': confidence
                        })
            
            logger.info(f"Detected {len(detected_texts)} relevant text elements")
            return detected_texts
            
        except Exception as e:
            logger.error(f"Error in text detection: {e}")
            return []
    
    def _is_chess_relevant_text(self, text: str) -> bool:
        """
        Check if detected text is relevant for chess notation
        
        Args:
            text: detected text string
            
        Returns:
            True if text appears to be chess-related
        """
        if not text or len(text.strip()) == 0:
            return False
        
        # Remove spaces for pattern matching
        clean_text = text.replace(' ', '')
        
        # Chess move patterns
        chess_patterns = [
            r'^[KQRBN]?[a-h]?[1-8]?[x]?[a-h][1-8][=QRBN]?[+#]?$',  # Standard moves
            r'^O-O(-O)?[+#]?$',  # Castling
            r'^\d+\.?$',  # Move numbers
            r'^\d+\.\.\.$',  # Black move indicators
            r'^[a-h][1-8]$',  # Simple pawn moves
            r'^[KQRBN][a-h][1-8]$',  # Simple piece moves
        ]
        
        # Check if text matches any chess pattern
        for pattern in chess_patterns:
            if re.match(pattern, clean_text):
                return True
        
        # Also include numbers (move numbers)
        if clean_text.isdigit() and 1 <= int(clean_text) <= 100:
            return True
        
        # Include common chess notation elements
        chess_elements = ['x', '+', '#', '=', 'O-O', 'O-O-O']
        if any(element in clean_text for element in chess_elements):
            return True
        
        return False
    
    def create_validation_overlay(self, image_bytes: bytes, detected_texts: List[Dict[str, Any]]) -> bytes:
        """
        Create an image overlay showing detected text with bounding boxes
        
        Args:
            image_bytes: Original image data
            detected_texts: List of detected text with bounding boxes
            
        Returns:
            Image bytes with overlay
        """
        try:
            # Convert to PIL Image
            image = Image.open(io.BytesIO(image_bytes))
            
            # Create a copy for drawing
            overlay_image = image.copy()
            draw = ImageDraw.Draw(overlay_image)
            
            # Try to load a font, fallback to default if not available
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            # Draw bounding boxes and text
            for detection in detected_texts:
                bbox = detection['bbox']
                text = detection['text']
                confidence = detection['confidence']
                
                # Convert bbox to rectangle coordinates
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                x1, y1 = min(x_coords), min(y_coords)
                x2, y2 = max(x_coords), max(y_coords)
                
                # Draw bounding box
                draw.rectangle([x1, y1, x2, y2], outline='red', width=2)
                
                # Draw text label with background
                text_label = f"{text} ({confidence:.2f})"
                
                # Get text size for background
                bbox_text = draw.textbbox((0, 0), text_label, font=font)
                text_width = bbox_text[2] - bbox_text[0]
                text_height = bbox_text[3] - bbox_text[1]
                
                # Position text above the bounding box
                text_x = x1
                text_y = max(0, y1 - text_height - 5)
                
                # Draw background rectangle for text
                draw.rectangle([text_x, text_y, text_x + text_width, text_y + text_height], 
                             fill='yellow', outline='red')
                
                # Draw text
                draw.text((text_x, text_y), text_label, fill='black', font=font)
            
            # Convert back to bytes
            output_buffer = io.BytesIO()
            overlay_image.save(output_buffer, format='JPEG', quality=95)
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating validation overlay: {e}")
            return image_bytes  # Return original image if overlay fails
    
    def validate_extraction_accuracy(self, image_bytes: bytes, extracted_pgn: str) -> Dict[str, Any]:
        """
        Validate the accuracy of text extraction by comparing with detected text
        
        Args:
            image_bytes: Original image data
            extracted_pgn: PGN text extracted by OCR
            
        Returns:
            Dictionary with validation results
        """
        detected_texts = self.detect_text_with_boxes(image_bytes)
        
        # Extract all text from detected elements
        detected_words = []
        for detection in detected_texts:
            words = detection['text'].split()
            detected_words.extend(words)
        
        # Extract words from PGN (remove metadata)
        pgn_lines = extracted_pgn.split('\n')
        pgn_words = []
        for line in pgn_lines:
            if not line.strip().startswith('['):  # Skip metadata lines
                words = re.findall(r'\S+', line)
                pgn_words.extend(words)
        
        # Calculate accuracy metrics
        detected_set = set(detected_words)
        pgn_set = set(pgn_words)
        
        common_words = detected_set.intersection(pgn_set)
        missing_in_pgn = detected_set - pgn_set
        extra_in_pgn = pgn_set - detected_set
        
        accuracy = len(common_words) / max(len(detected_set), 1) * 100
        
        return {
            'accuracy_percentage': accuracy,
            'detected_words_count': len(detected_words),
            'pgn_words_count': len(pgn_words),
            'common_words': len(common_words),
            'missing_in_pgn': list(missing_in_pgn),
            'extra_in_pgn': list(extra_in_pgn),
            'detected_texts': detected_texts
        }
